// ***********************************************
// This example commands.js shows you how to
// create various custom commands and overwrite
// existing commands.
//
// For more comprehensive examples of custom
// commands please read more here:
// https://on.cypress.io/custom-commands
// ***********************************************
//
//
// -- This is a parent command --
// Cypress.Commands.add('login', (email, password) => { ... })
//
//
// -- This is a child command --
// Cypress.Commands.add('drag', { prevSubject: 'element'}, (subject, options) => { ... })
//
//
// -- This is a dual command --
// Cypress.Commands.add('dismiss', { prevSubject: 'optional'}, (subject, options) => { ... })
//
//
// -- This will overwrite an existing command --
// Cypress.Commands.overwrite('visit', (originalFn, url, options) => { ... })


const thingToMakeTypes = {
  cutter: "cutter",
  imprintCutter: "imprintCutter",
  cakeTopper: "cakeTopper",
  cutterAndStamp: "cutterAndStamp",
  stamp: "stamp",
  setOfCutters: {
    type: "setOfCutters",
    parent: "cutterSet",
    parentText: "Cutter Set"
  },
  setOfImprintCutters: {
    type: "setOfImprintCutters",
    parent: "cutterSet",
    parentText: "Cutter Set"
  },
  practiceCookie: "practiceCookie",
  multiCutter: "multiCutter",
}

const generationTimeout = 300 * 1000 // 5 minutes in ms

Cypress.Commands.add('uploadImage', (filename) => {
  cy.get('#dropzone')
    .selectFile(`cypress/fixtures/${filename}`, { action: 'drag-drop' });
})

Cypress.Commands.add('checkForErrorMessage', () => {
  cy.get('#loadingAnimation', { timeout: generationTimeout }).should('have.css', 'display', 'none').then(() => {
    // make sure no error message is shown
    cy.get('#errorText', { timeout: 1000 }).should('have.css', 'display', 'none')
  });
})

Cypress.Commands.add('openThingToMakeMenu', () => {
  cy.get('#cy-make-type-button').click()
})

Cypress.Commands.add('selectThingToMake', (type) => {
  // open thing to make menu
  cy.openThingToMakeMenu()

  // now select the right type
  // i.e. `#cy-cutter`
  if (type.type) {
    // first hover parent menu, then click child
    cy.contains(type.parentText).trigger('mouseenter').then(() => {
      cy.get(`#cy-${type.type}`).click({ force: true })
    })
  } else {
    // click menu button
    cy.get(`#cy-${type}`).click({ force: true })
  }
})

Cypress.Commands.add('tryGeneratingAllCutterTypes', () => {
  // try generating all types
  Object.values(thingToMakeTypes).forEach((type) => {
    cy.log(`Generating ${type}`)
    cy.then(() => {
      cy.selectThingToMake(type)
      cy.checkForErrorMessage()
    })
  })
})