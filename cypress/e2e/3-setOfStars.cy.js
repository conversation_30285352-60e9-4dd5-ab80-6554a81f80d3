const starSetUrl = "http://localhost:8080/?settings=%7B%22purchaseSize%22%3A3,%22cutterSize%22%3A69.85,%22sizeType%22%3A%22longest%22,%22interiorType%22%3A%22outline%22,%22imprintDepth%22%3A4.5,%22bladeDepth%22%3A%2216%22,%22bladeThickness%22%3A0.8,%22extraBladeDepth%22%3A%2214%22,%22extraBladeThickness%22%3A%220.8%22,%22baseHeight%22%3A%225%22,%22baseWidth%22%3A%226%22,%22multiCutterRow%22%3A1,%22multiCutterColumn%22%3A1,%22multiCutterSpacing%22%3A-2,%22horizontalBarWidth%22%3A0,%22verticalBarWidth%22%3A0,%22sharpCutter%22%3Atrue,%22cutterChamferTipWidth%22%3A%220.8%22,%22cutterChamferHeight%22%3A2,%22handleShape%22%3A%22round%22,%22cutterMaterial%22%3A%22normal%22,%22units%22%3A%22inches%22,%22invertInterior%22%3Afalse,%22offsetIn%22%3Afalse,%22offsetInner%22%3A0,%22forceSolidBar%22%3Afalse,%22mirror%22%3Afalse,%22removeImprintBase%22%3Afalse,%22stampCutterTolerance%22%3A0.9,%22stampBackHeight%22%3A4.5,%22stampImprintHeight%22%3A3,%22deleteOuterPath%22%3Afalse,%22cutterSet%22%3Atrue,%22resolution%22%3A100,%22showLines%22%3Atrue,%22cakeTopper%22%3A%7B%22message%22%3A%22Happy%5CnBirthday%21%22,%22fontUrl%22%3A%22%2Ffonts%2Fabril-fatface-v12-latin-regular.43e52b20.woff%22,%22offsetDist%22%3A5,%22baseHeight%22%3A5,%22textHeight%22%3A5,%22fontSize%22%3A72,%22lineDistance%22%3A5,%22pinLength%22%3A50,%22pinWidth%22%3A5%7D,%22userSize%22%3A2.75,%22name%22%3A%22Scalloped%20star%202%22,%22currentStlOutputFile%22%3A%22c24ec88561b54d1599b8c21622030be6%2050W%20V8.stl%22%7D"

describe("Test broken star set", () => {
  it("Generate all star set cutters", () => {
    cy.visit(starSetUrl);

    cy.checkForErrorMessage();

    cy.tryGeneratingAllCutterTypes();
  });
})