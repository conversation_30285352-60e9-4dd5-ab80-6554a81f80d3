const barMitzvahUrl = "http://localhost:8080/?settings=%7B%22purchaseSize%22%3A3,%22cutterSize%22%3A75,%22sizeType%22%3A%22longest%22,%22interiorType%22%3A%22outline%22,%22imprintDepth%22%3A4.5,%22bladeDepth%22%3A12.5,%22bladeThickness%22%3A0.8,%22extraBladeDepth%22%3A10,%22extraBladeThickness%22%3A0,%22baseHeight%22%3A3.5,%22baseWidth%22%3A4,%22multiCutterRow%22%3A2,%22multiCutterColumn%22%3A2,%22multiCutterSpacing%22%3A10,%22horizontalBarWidth%22%3A8,%22verticalBarWidth%22%3A0,%22sharpCutter%22%3Atrue,%22cutterChamferTipWidth%22%3A0.4,%22cutterChamferHeight%22%3A2,%22handleShape%22%3A%22round%22,%22cutterMaterial%22%3A%22normal%22,%22units%22%3A%22mm%22,%22invertInterior%22%3Afalse,%22offsetIn%22%3Afalse,%22offsetInner%22%3A0,%22forceSolidBar%22%3Afalse,%22mirror%22%3Afalse,%22removeImprintBase%22%3Afalse,%22stampCutterTolerance%22%3A0.9,%22stampBackHeight%22%3A4.5,%22stampImprintHeight%22%3A3,%22deleteOuterPath%22%3Afalse,%22cutterSet%22%3Afalse,%22resolution%22%3A100,%22showLines%22%3Atrue,%22cakeTopper%22%3A%7B%22message%22%3A%22Happy%5CnBirthday%21%22,%22fontUrl%22%3A%22%2Ffonts%2Fabril-fatface-v12-latin-regular.43e52b20.woff%22,%22offsetDist%22%3A5,%22baseHeight%22%3A5,%22textHeight%22%3A5,%22fontSize%22%3A72,%22lineDistance%22%3A5,%22pinLength%22%3A50,%22pinWidth%22%3A5%7D,%22currentStlOutputFile%22%3A%22b0eef0e8c7c14be0adb8d0f626d5e2bc%2050W%20V8.stl%22,%22name%22%3A%22discord-logo-black%22,%22userSize%22%3A75%7D"

describe("Test discord logo cutter", () => {
  it("Generate all discord logo cutters", () => {
    cy.visit(barMitzvahUrl);

    cy.checkForErrorMessage();

    cy.tryGeneratingAllCutterTypes();
  });
})