const turtleUrl = "http://localhost:8080/?settings=%7B%22purchaseSize%22%3A3,%22cutterSize%22%3A31.75,%22sizeType%22%3A%22width%22,%22interiorType%22%3A%22solid%22,%22imprintDepth%22%3A%222.0%22,%22bladeDepth%22%3A%2212.00%22,%22bladeThickness%22%3A%220.4%22,%22extraBladeDepth%22%3A%228.00%22,%22extraBladeThickness%22%3A%220.8%22,%22baseHeight%22%3A%224.0%22,%22baseWidth%22%3A4,%22multiCutterRow%22%3A1,%22multiCutterColumn%22%3A1,%22multiCutterSpacing%22%3A-2,%22horizontalBarWidth%22%3A%222%22,%22verticalBarWidth%22%3A%222%22,%22sharpCutter%22%3Atrue,%22cutterChamferTipWidth%22%3A0.4,%22cutterChamferHeight%22%3A2,%22handleShape%22%3A%22round%22,%22cutterMaterial%22%3A%22normal%22,%22units%22%3A%22inches%22,%22invertInterior%22%3Afalse,%22offsetIn%22%3Afalse,%22offsetInner%22%3A0,%22forceSolidBar%22%3Afalse,%22mirror%22%3Afalse,%22removeImprintBase%22%3Atrue,%22stampCutterTolerance%22%3A0.9,%22stampBackHeight%22%3A4.5,%22stampImprintHeight%22%3A3,%22deleteOuterPath%22%3Afalse,%22cutterSet%22%3Afalse,%22resolution%22%3A100,%22showLines%22%3Atrue,%22cakeTopper%22%3A%7B%22textHeight%22%3A5,%22lineDistance%22%3A5,%22pinWidth%22%3A5,%22offsetDist%22%3A5,%22pinLength%22%3A50,%22fontUrl%22%3A%22%2Fassets%2Fabril-fatface-v12-latin-regular-CGc9OYSy.woff%22,%22fontSize%22%3A72,%22message%22%3A%22Happy%5CnBirthday%21%22,%22baseHeight%22%3A5%7D,%22outlineShape%22%3A%22circle%22,%22outlineOffset%22%3A5,%22userSize%22%3A1.25,%22currentStlOutputFile%22%3A%225c54f85230d146a7a7256211a58825d9%2050W%20V8.stl%22,%22name%22%3A%22Sea_Turtle_Sand_Dollar_Cutout_-_updated_7-31-24%22%7D"

describe("Test turtle logo cutter", () => {
  it("Generate all turtle logo cutters", () => {
    cy.visit(turtleUrl);

    cy.checkForErrorMessage();

    cy.tryGeneratingAllCutterTypes();
  });
})